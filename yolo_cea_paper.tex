\documentclass[lettersize,journal,nofonts]{IEEEtran}
% 加载 fontspec 来处理文本字体，这是 LuaLaTeX/XeLaTeX 的标准做法
\usepackage{fontspec}
% 加载 unicode-math 来处理数学字体
\usepackage{unicode-math}

% 设置字体，以匹配 IEEE 风格 (Times-like)
% TeX Gyre Termes 是 newtx 包所基于的字体，是最佳替代品
% 大多数 TeX 发行版 (TeX Live, MiKTeX) 都自带此字体
\setmainfont{TeX Gyre Termes}
\setmathfont{TeX Gyre Termes Math}
% （可选）同时设置无衬线字体和等宽字体，保持风格统一
\setsansfont{TeX Gyre Heros} % 对应 Helvetica
\setmonofont{TeX Gyre Cursor} % 对应 Courier
% --- 核心改动结束 ---

\usepackage{amsmath,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
\usepackage{array}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage[caption=false,font=footnotesize,labelfont=rm,textfont=rm]{subfig}
\usepackage{textcomp}

\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{graphicx}
\usepackage{cite}

% citecolor为引用颜色 第一种为 green
% linkcolor为图表引用颜色 第一种为 red
\usepackage[colorlinks,
            linkcolor=blue,
            anchorcolor=blue,
            citecolor=blue]{hyperref}
% 修改参考文献两端[]颜色 第一种为 green
\renewcommand{\citeleft}{\textcolor{blue}{[}} 
\renewcommand{\citeright}{\textcolor{blue}{]}}

\hyphenation{op-tical net-works semi-conduc-tor IEEE-Xplore}
% updated with editorial comments 8/9/2021

\begin{document}

\title{YOLO-CEA: Context-Enhanced and Aligned Algorithm for Insulator Defect Detection in Power Transmission Lines}

% \author{Your Name,~\IEEEmembership{Member,~IEEE,}
%         % <-this % stops a space
% \thanks{This paper was produced for insulator defect detection research.}% <-this % stops a space
% \thanks{Manuscript received [Date]; revised [Date].}}

% % The paper headers
% \markboth{IEEE Transactions on [Journal Name],~Vol.~XX, No.~X, [Month]~[Year]}%
% {Author \MakeLowercase{\textit{et al.}}: YOLO-CEA for Insulator Defect Detection}

% \IEEEpubid{0000--0000/00\$00.00~\copyright~[Year] IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.

\maketitle

\begin{abstract}
Accurate detection of insulator defects is pivotal for power system stability, yet it is impeded by challenges such as complex backgrounds and significant scale variations in defects.
To overcome these obstacles, this paper presents YOLO-CEA (Context-Enhanced and Aligned YOLO), a lightweight and high-performance detection framework specifically engineered to improve accuracy while concurrently reducing model parameters and computational load. The model's architecture incorporates several targeted solutions to these problems. 
Within its backbone, a C3k2-HTC module synergistically fuses Transformer-derived global context with CNN-based local features, substantially improving feature representation in cluttered scenes. The model's neck is redesigned with an Aligned-Balanced Feature Pyramid Network (AB-FPN), which leverages precise feature alignment and efficient convolution to strike an optimal balance between information fidelity and computational cost. 
Furthermore, a novel Shared Location Quality Modulation Detection Head (SLQMD) enhances parameter efficiency and aligns classification confidence with localization quality, thereby increasing the reliability of final detections. Experimental evaluation on the public IDID dataset demonstrates that YOLO-CEA achieves an AP50-95 of 87.81\%, a significant 4.78 percentage point improvement over the baseline, while simultaneously reducing GFLOPs to 5.4 and model size to 4.6 MB. 
Notably, YOLO-CEA surpasses recent heavyweight models in key metrics with only a fraction of their computational demand, and its strong generalization ability is further validated on the CPLID dataset. The proposed model thus achieves a state-of-the-art balance between detection accuracy and computational efficiency, highlighting its significant potential for deployment in resource-constrained automated inspection systems.
\end{abstract}

\begin{IEEEkeywords}
Insulator Defect Detection, Lightweight Object Detection, Transformer,  Feature Fusion, Computational Efficiency, Resource-Constrained Systems.
\end{IEEEkeywords}

\section{Introduction}
\IEEEPARstart{T}{he} stable and reliable operation of the power grid system is imperative for societal development and economic activity, with transmission lines serving as its fundamental arteries \cite{2022Inspection}. 
Among the critical components of these lines, insulators perform the dual, indispensable functions of electrical insulation and mechanical support \cite{2024Region}. 
However, their perennial exposure to harsh outdoor environments, including adverse weather and mechanical stress, renders them susceptible to various defects such as self-explosion, damage, and cracking \cite{2023Summary,2022An}. 
The failure of even a single insulator can compromise line stability, potentially leading to widespread power outages and significant economic losses \cite{2021Real}. Consequently, the development of intelligent, accurate, and efficient methods for insulator defect detection is of paramount importance for preemptive maintenance and ensuring grid security.

In recent years, inspections conducted by Unmanned Aerial Vehicles (UAVs) coupled with computer vision techniques have emerged as a promising alternative to traditional manual inspections, which are often hazardous and inefficient \cite{9400959}. The advancement of deep learning, particularly convolutional neural networks (CNNs), has catalyzed a paradigm shift, enabling real-time and precise analysis of aerial imagery \cite{2021InsuDet,2022Improved}. 
A plethora of studies have leveraged deep learning models, especially one-stage detectors like the You Only Look Once (YOLO) family, for this task due to their excellent balance of speed and accuracy \cite{2021Improved,coatings13050880}. 
Researchers have introduced various enhancements to these frameworks. 
For instance, to improve detection performance in complex backgrounds and for small targets, some works have integrated attention mechanisms like CBAM or ECA and employed advanced feature fusion networks such as BiFPN \cite{han2022insulator,DBLP:journals/eaai/FengYYYLSZ25}. 
These methods aim to refine feature maps, allowing the model to focus on more salient regions of the image.
Another significant line of inquiry addresses the trade-off between detection accuracy and computational efficiency, a critical factor for on-board deployment on resource-constrained UAVs. To this end, lightweight models have been proposed, utilizing techniques like network pruning, knowledge distillation, or designing efficient modules to reduce model parameters and computational complexity. 
For example, methods like the CACS-YOLO presented in \cite{2024CACS-YOLO} attempt to create a lightweight architecture by incorporating channel shuffle operations while using attention to maintain accuracy. 
These efforts have undeniably advanced the field, achieving commendable results on various benchmarks.
However, despite these advancements, several fundamental challenges persist, limiting the performance and reliability of existing detectors in real-world scenarios. 
\textbf{First}, conventional CNN-based backbones, while excellent at extracting local features, often struggle to capture long-range contextual dependencies. This limitation becomes a significant bottleneck when insulators are set against cluttered backgrounds containing visually similar objects like towers, cross-arms, and foliage, leading to false positives. 
\textbf{Second}, the feature pyramid networks (FPNs) commonly used for multi-scale feature fusion are often suboptimal. The standard interpolation methods (e.g., nearest neighbor) used for upsampling can cause spatial misalignment of features, while simplistic downsampling paths can lead to a loss of critical semantic information, impairing the model's ability to detect defects of varying scales. 
\textbf{Finally}, a critical yet often overlooked issue is the frequent misalignment between a model's classification confidence and its localization accuracy. Many detectors may report a high confidence score for a defect while providing a poorly placed bounding box, undermining the reliability of the detection results, which is unacceptable for safety-critical applications. The pursuit of lightweight models often exacerbates these issues, as aggressive model compression can further degrade feature representation quality.

To address these multifaceted limitations, this paper proposes a novel, efficient, and highly accurate framework for insulator defect detection, named \textbf{YOLO-CEA (Context-Enhanced and Aligned)}. 
Our approach is holistically designed to enhance feature representation, optimize feature fusion, and ensure detection reliability while maintaining computational efficiency. 
The main contributions of this paper are summarized as follows:

\begin{enumerate}
    \item We propose a \textbf{Context-Enhanced Backbone} incorporating a novel \textbf{C3k2-HTC module}. This hybrid block synergistically integrates a Transformer with a CNN, enabling the model to capture both long-range dependencies and fine-grained local features. This significantly enhances feature representation, especially in cluttered background scenarios.
    \item We design an \textbf{Aligned-Balanced Feature Pyramid Network (AB-FPN)} for the neck. This novel FPN replaces conventional operators with \textbf{Soft Nearest Neighbor Interpolation (SNI)} to mitigate feature misalignment during upsampling and employs \textbf{GSConv Enhancement (GSE)} for an efficient yet information-rich downsampling path, leading to superior fusion of multi-scale features.
    \item We introduce a \textbf{Reliable and Efficient Head}, named \textbf{SLQMD}. It utilizes cross-scale parameter sharing to reduce model complexity while integrating a novel \textbf{Location Quality Modulation (LQM)} mechanism. This mechanism explicitly aligns classification scores with localization accuracy, ensuring that the model yields highly reliable detections.
    \item We conduct extensive experiments on the public IDID and CPLID datasets. The results demonstrate that our proposed YOLO-CEA not only achieves state-of-the-art detection accuracy, significantly outperforming the baseline and other competing models, but also maintains exceptional computational efficiency, establishing a new benchmark for lightweight insulator defect detection.
\end{enumerate}

The remainder of this paper is organized as follows. Section~\ref{sec:method} presents the proposed YOLO-CEA model in detail, including its backbone, neck, and head architectures. Section~\ref{sec:experimental} describes the datasets, experimental setup, and evaluation metrics. Section~\ref{sec:conclusion} presents and discusses the comprehensive experimental results. Finally, Section~\ref{sec:conclusion} concludes the paper and outlines future work.

\section{Method}
\label{sec:method}

This section presents the novel framework designed to enhance insulator defect detection performance. The proposed framework systematically reconstructs and optimizes the backbone, feature fusion neck, and detection head components of YOLOv11\cite{yolo11}. The overall architecture of YOLO-CEA is illustrated in Figure~\ref{fig_architecture}. The core improvements encompass feature context enhancement through a hybrid attention mechanism, multi-scale feature alignment via optimized sampling strategies, and enhanced detection reliability by refining the prediction head architecture.

\begin{figure*}[!htbp]
\centering
\includegraphics[width=\textwidth]{figures/overfig}
\caption{Overall architecture of the  YOLO-CEA.}
\label{fig_architecture}
\end{figure*}

As shown in Figure~\ref{fig_architecture}, in the \textbf{Backbone} section, the input image first passes through a series of convolutional layers for downsampling to progressively extract multi-level feature maps. To fundamentally enhance the model's feature representation capabilities, we replaced the original C3k2 module in the deep layers of the backbone with our C3k2-HTC module. The C3k2-HTC module synergistically integrates the global context-capturing capabilities of Transformers with the fine-grained local feature extraction capabilities of convolutions, which is crucial for accurately identifying insulator defects of various morphologies in complex backgrounds. Subsequently, the SPPF module enhances the receptive field of features through multi-scale pooling operations, while the C2PSA module at the end of the backbone further strengthens the capture of key multi-scale information through its unique attention mechanism. This provides a richer and higher-quality feature foundation for the subsequent feature fusion network.

Next, these multi-scale features extracted from the backbone are fed into our designed \textbf{Aligned and Balanced Feature Pyramid Network (AB-FPN)} for feature fusion. AB-FPN ensures efficient interaction between features at different levels through two specially optimized paths (top-down and bottom-up). In the top-down path, we use the SNI module for upsampling. It employs an energy-aware soft interpolation method to effectively prevent high-level semantic features from excessively suppressing low-level detail features, thereby achieving precise feature alignment. In the bottom-up path, we use the GSE module for downsampling. This module significantly reduces computational costs while preserving rich feature information, achieving a perfect balance between computational efficiency and representational power. Through the synergistic action of SNI and GSE, AB-FPN generates multi-scale fused features that are better aligned and more information-balanced.

Finally, these fused features are passed to the \textbf{Shared Location Quality Modulation Detection Head (SLQMD)} for final bounding box regression and class prediction. This detection head is designed to address the core issues of parameter redundancy and the mismatch between classification confidence and localization precision in traditional detectors. It first enhances parameter efficiency through a conv. layer shared across scales. Then, it utilizes the innovative LQM module to dynamically adjust and calibrate classification scores based on the quality of the predicted bounding box probability distribution. This mechanism ensures that only prediction boxes with both high classification confidence and high localization accuracy receive a final high score, thereby significantly improving the reliability of the detection.

Through the coordinated operation of the above modules, YOLO-CEA demonstrates outstanding detection performance and efficiency in complex power line inspection scenarios. In the following subsections, we will provide an in-depth introduction to the design principles and implementation details of the C3k2-HTC module, the AB-FPN architecture, and the SLQMD.
\subsection{C3k2-HTC Module}

To effectively address the challenges of insulator defect detection in the complex backgrounds of power transmission lines, this paper optimizes the feature extraction capability of the YOLOv11 backbone. In the deep stages of the original YOLOv11 backbone, the model primarily relies on the C3k2 module for feature extraction. The core computational unit of the C3k2 module consists of multiple cascaded Bottleneck or C3k modules, which is a purely convolution-based design. Although this structure is efficient at capturing local textures and spatial features, its capabilities reveal significant shortcomings when dealing with complex scenes that require global contextual information. Due to the fixed size of convolutional kernels, computation is limited to a local receptive field, making it difficult to establish dependencies between distant pixels in an image. For example, when determining whether a tiny object is a genuine insulator self-explosion defect or complex background noise, the model needs to not only identify its local features but also understand its global position within the entire insulator string and even the transmission tower. The pure-convolution C3k2 module is inadequate in this regard, easily leading to missed detections or false positives. To this end, this paper innovatively designs a \textbf{C3k2-HTC module (C3k2 module with Hybrid Transformer Conv Block)}, which aims to organically combine the powerful global modeling capabilities of Transformers\cite{2021An} with the efficient local feature extraction of CNNs, thereby fundamentally compensating for the network's deficiency in global information perception.

C3k2-HTC replaces the C3k in C3k2 with the \textbf{HTCBlock (Hybrid Transformer Conv Block)}. The HTCBlock is key to achieving parallel processing of local and global information. It first divides the channels of the input feature map according to an adjustable mixing ratio hyperparameter $r$. A proportion $r$ of the channels is sent to a \textbf{TransformerBlock} branch. This branch incorporates an enhanced Multi-Head Self-Attention (MHSA) module to capture long-range dependencies. The MHSA module employs a multi-head attention framework enhanced with positional encoding. Given an input feature map $\mathbf{X}$, the module first uses a 1×1 convolution ($\text{Conv}_{\text{qkv}}$) to efficiently and jointly generate representations for the query, key, and value:
\begin{equation}
\mathbf{QKV} = \text{Conv}_{\text{qkv}}(\mathbf{X})
\end{equation}
In this process, to improve computational efficiency, the dimension of the key is compressed compared to conventional attention mechanisms. The resulting QKV tensor is reshaped and then split into the multi-head query matrix $\mathbf{Q}$, key matrix $\mathbf{K}$, and value matrix $\mathbf{V}$. Subsequently, attention is calculated as follows to obtain the global contextual features $\mathbf{Z}$:
\begin{equation}
\mathbf{A} = \text{Softmax}\left(\frac{\mathbf{Q}^T \mathbf{K}}{\sqrt{d_k}}\right)
\end{equation}
\begin{equation}
\mathbf{Z} = \mathbf{V} \mathbf{A}^T
\end{equation}
To preserve spatial locality, we introduce a parallel \textbf{positional encoding branch}. This branch is concise, applying only a lightweight depthwise convolution to the value features $\mathbf{V}$ to efficiently extract local spatial information, which we denote as $\mathbf{PE}$. Finally, the output of the module is obtained by fusing the global contextual features $\mathbf{Z}$ with the local positional encoding $\mathbf{PE}$, followed by a projection through a final 1×1 convolution ($\text{Conv}_{\text{out}}$):
\begin{equation}
\mathbf{Y} = \text{Conv}_{\text{out}}(\text{Reshape}(\mathbf{Z}) + \mathbf{PE})
\end{equation}

The Feed-Forward Network (FFN) part of the TransformerBlock has also been critically optimized. Compared to the conventional design in Vision Transformers that uses simple linear layers and GELU activation functions, we introduce the \textbf{Gated Linear Unit (CGLU)}\cite{shi2024transnext}. CGLU uses a dynamic, data-driven gating mechanism to regulate information flow, replacing the fixed activation function. This endows the FFN with stronger expressive power and non-linear modeling potential, allowing it to process and transform features more adaptively, thereby further improving the quality of extracted global contextual information. Meanwhile, the remaining proportion $1-r$ of channels enters a standard \textbf{Bottleneck} convolution branch to preserve and refine local spatial details and texture features. The outputs of these two branches are concatenated along the channel dimension and deeply fused through a $1 \times 1$ convolutional layer. This parallel hybrid structure enables the model to perceive both local details and global context within a single module, achieving a complementary advantage between local and global information.

In the overall design of C3k2-HTC, the input features first pass through a $1 \times 1$ convolution and are then split into two paths. One path serves as a direct shortcut connection, while the other is sent into a sequence of multiple cascaded HTCBlocks for deep feature extraction. Finally, the features from these two paths are concatenated and fused, a structure that greatly facilitates gradient flow and cross-layer feature reuse.

Considering that deep network layers process more high-level, abstract semantic features, establishing global dependencies on these features is more meaningful than on shallow-level, low-level texture features. Furthermore, the computational complexity of the self-attention mechanism in Transformers is quadratic with respect to the feature map size. Applying it in the deep layers of the network, where the spatial resolution is already significantly reduced, allows for efficient global context modeling at a controllable computational cost. Therefore, we deploy this module in the deep stages of the backbone to replace the original C3k2 module, striking a balance between model performance and efficiency.

\begin{figure*}[!htbp]
\centering
\includegraphics[width=\textwidth]{figures/HTC}
\caption{Architecture of the C3k2-HTC module showing the hybrid design that combines Transformer and CNN components. The HTCBlock processes features through parallel Transformer and Bottleneck branches, enabling both global context modeling and local feature extraction.}
\label{fig_htc}
\end{figure*}

\subsection{Aligned-Balanced Feature Pyramid Network (AB-FPN)}

YOLOv11 employs the classic Path Aggregation Network (PANet) as its neck structure, achieving effective multi-scale feature fusion through top-down and bottom-up pathways. However, when dealing with targets like insulator defects, which exhibit vast size variations and are rich in detailed information, we found that the feature fusion mechanism of the original YOLOv11 neck still has room for optimization. Specifically, its top-down path uses \textbf{standard nearest-neighbor interpolation}, and its bottom-up path uses \textbf{standard strided convolution}, which can lead to \textbf{feature misalignment} and \textbf{computational redundancy}, respectively.

To address these challenges, we have deeply optimized the neck of YOLOv11 and proposed a new feature fusion architecture—the \textbf{Aligned-Balanced Feature Pyramid Network (AB-FPN)}. The core idea of AB-FPN is to replace the original upsampling and downsampling operations with two specially designed lightweight modules, namely \textbf{Soft Nearest Neighbor Interpolation (SNI)} and \textbf{GSConv Enhancement (GSE)}\cite{li2024rethinking}. This achieves better feature alignment and computational balance with almost no additional cost.

In the original top-down path of YOLOv11, high-level feature maps are enlarged using standard nearest-neighbor interpolation. This "hard" pixel replication causes the energy (sum of activation values) of the feature map to grow quadratically with the magnification factor, leading to high-level semantic features dominating the fusion with low-level detail features. This imbalanced fusion can easily cause feature misalignment, which is particularly detrimental to preserving weak features such as tiny cracks on insulators. The SNI module aims to "soften" this process. After performing nearest-neighbor interpolation, it introduces a softening coefficient $\alpha$, which is related to the scaling factor, to normalize the energy of the result. Its operation can be defined by Equation~\eqref{eq:sni}:

\begin{equation}
\label{eq:sni}
\mathbf{Y}= \alpha \cdot f_{\text{nearest}}(\mathbf{X}) \quad \text{where} \quad \alpha = \frac{1}{k^2}
\end{equation}
where $\mathbf{X}$ is the deep feature map to be upsampled, $\mathbf{Y}$ is the upsampled result, $f_{\text{nearest}}$ represents the nearest-neighbor interpolation operation, and $k$ is the upsampling factor.

By introducing $\alpha$, SNI \textbf{regulates} the weights of features from different levels during fusion, effectively mitigating the drastic energy changes caused by upsampling and preventing high-level features from excessively "covering" low-level features. This makes the fusion process smoother, promotes \textbf{Secondary Features Alignment}, and significantly reduces the difficulty for subsequent convolutional layers to learn the fused representation, thereby enhancing the model's comprehensive perception of multi-scale defects.

\begin{figure*}[!htbp]
\centering
\includegraphics[width=\textwidth]{figures/SNI}
\caption{Illustration of the Soft Nearest Neighbor Interpolation (SNI) module. The module applies energy normalization with coefficient $\alpha = 1/k^2$ to prevent feature misalignment during upsampling in the top-down path of AB-FPN.}
\label{fig_sni}
\end{figure*}

In the original bottom-up path of YOLOv11, feature map downsampling is accomplished by a $3\times3$ convolution with a stride of 2. While effective, standard convolution has a relatively large number of parameters and computational load (FLOPs) when processing high-channel feature maps, presenting an optimization opportunity for detection models that pursue ultimate efficiency.

To reduce computational cost while maintaining or even enhancing feature representation capabilities, we employ the GSE module to perform the downsampling task. GSE is an efficient hybrid convolution structure, with its core idea being to strike a delicate balance between computational efficiency and feature richness. GSE cleverly decomposes the feature processing into two parallel branches. First, the input feature map flows through a standard convolutional layer, which reduces the number of input channels to half of the target number of channels. This step is responsible for extracting information-dense "base features" with cross-channel interactions. Subsequently, these generated "base features" are sent to an extremely lightweight secondary processing branch. This branch consists of a 2D convolution, a depth-wise separable convolution (DWC)\cite{chollet2017xception}, and a GELU activation function\cite{hendrycks2016gaussian} in series. Since the computational cost of depth-wise separable convolution is extremely low, this branch can generate "enhanced features" with diverse receptive fields and texture information with almost no additional burden. Finally, the "base features" from the dense path and the "enhanced features" from the sparse path are concatenated along the channel dimension. To break the channel-wise separation of features from the two paths and promote full information interaction, a crucial \textbf{Channel Shuffle} \cite{zhang2018shufflenet}operation is applied to the concatenated feature map. This operation efficiently recombines features from different computational paths, ensuring that information is thoroughly mixed before being passed to the next layer.

Essentially, GSE adopts a "divide and conquer" strategy: it lets the computationally expensive standard convolution focus on extracting the most critical cross-channel information, while assigning the task of generating feature diversity to the extremely low-cost depth-wise convolution. Compared to the single standard convolution in the original YOLOv11, this design achieves an excellent \textbf{balance} between \textbf{computational efficiency} (from sparse depth-wise convolution) and \textbf{feature richness} (from dense standard convolution). It can provide the model with stronger representation capabilities while significantly reducing the number of parameters and computational load, thereby improving the efficiency of feature aggregation in the bottom-up path.

\begin{figure*}[!htbp]
\centering
\includegraphics[width=\textwidth]{figures/GSConvE}
\caption{Structure of the GSConv Enhancement (GSE) module used in the bottom-up path of AB-FPN. The module employs a hybrid design with parallel dense and sparse convolution branches followed by channel shuffle for efficient downsampling.}
\label{fig_gsconve}
\end{figure*}

In summary, our proposed AB-FPN architecture performs targeted optimizations on the key components of the original YOLOv11 neck through SNI and GSE. It systematically enhances the performance of the feature pyramid from the dimensions of "alignment" and "balance," laying a solid foundation for high-precision, high-efficiency insulator defect detection.

\subsection{Shared Location Quality Modulation Detection Head (SLQMD)}

Detection heads in modern object detectors generally face two interrelated challenges: first, \textbf{parameter efficiency}, which refers to the parameter redundancy and computational overhead caused by deploying independent prediction branches for each level of the multi-scale feature pyramid; and second, \textbf{prediction reliability}, which is the problem of a lack of effective information interaction between the parallel tasks of classification and localization, leading to inconsistency between classification confidence and localization precision. To systematically address these challenges, we propose a novel detection head architecture—the \textbf{Shared Location Quality Modulation Detection Head (SLQMD)}. This architecture collaboratively optimizes parameter efficiency and prediction reliability through a unified design.

The core design of SLQMD includes two interrelated mechanisms: \textbf{cross-scale parameter sharing} and \textbf{location quality modulation}.

First, to improve parameter efficiency and enhance the generalization capability of feature representations\cite{tian2019fcos}, we introduce a \textbf{cross-scale parameter sharing mechanism}. Unlike traditional designs that equip each FPN level with an independent convolutional layer, SLQMD inputs feature maps from all scales into a \textbf{unified, parameter-shared convolutional module}. This design not only significantly reduces the number of parameters in the detection head but, more importantly, it forces the network to use the same set of transformation kernels across different scales, prompting the model to learn a scale-invariant feature extraction paradigm. The resulting shared feature representation has stronger generalization ability and provides high-quality input for the subsequent classification and regression tasks.

Second, based on these efficient shared features, we introduce the \textbf{Location Quality Modulation (LQM) mechanism} to address the inconsistency between classification and regression. This mechanism stems from a core insight: the probability distribution shape of bounding box regression can reflect its localization certainty\cite{li2021generalized}. An accurate localization prediction should have a low-entropy discrete probability distribution $P_{box}$, meaning the distribution is sharp and concentrated. Conversely, an uncertain prediction corresponds to a high-entropy, flat distribution. LQM uses a lightweight multi-layer perceptron ($\mathcal{M}_{LQM}$) to map statistical measures extracted from $P_{box}$ (such as the mean of the top-k probability values) into a scalar score, which we call the \textbf{location quality score} ($S_{qual}$). Its calculation can be formalized as:
\begin{equation}
S_{qual} = \mathcal{M}_{LQM}(\text{Stat}(P_{box}))
\end{equation}
This $S_{qual}$ score serves as an explicit quantification of localization quality and is used to modulate the initial classification score $S_{cls}$. The final confidence score $S_{final}$ is formed by their additive fusion:
\begin{equation}
S_{final} = S_{cls} + S_{qual}
\end{equation}
In this way, localization quality is explicitly integrated into the final confidence assessment. For a prediction to achieve high confidence, it must satisfy both a high classification score and high localization quality. This mechanismically promotes synergy between the classification and regression tasks, enhancing the reliability of the final detection results.

In summary, the parameter sharing mechanism of SLQMD first builds an efficient and generalizable feature extraction foundation, while the location quality modulation mechanism further calibrates the reliability of predictions on this basis. The organic combination of these two components forms a unified detection framework that is significantly improved in both efficiency and reliability.

\begin{figure*}[!htbp]
\centering
\includegraphics[width=\textwidth]{figures/SLQMD}
\caption{Architecture of the Shared Location Quality Modulation Detection Head (SLQMD). The design features cross-scale parameter sharing and location quality modulation mechanism to enhance both parameter efficiency and prediction reliability.}
\label{fig_slqmd}
\end{figure*}

\section{Experimental}
\label{sec:experimental}

\subsection{Dataset}

This study selected two widely recognized public datasets: the Chinese Power Line Insulator Dataset (CPLID) \cite{tao2018detection} and the Insulator Defect Image Dataset (IDID) \cite{vkdw-x769-21}. The IDID dataset focuses on insulator strings on power transmission lines, where the main objects in the images are insulator bodies, classified into three categories based on their surface condition: \textbf{Good insulator shell}, \textbf{Broken insulator shell}, and \textbf{Flashover damage insulator shell}. It contains \textbf{1596 images} and \textbf{7568 annotated objects}, specifically including 1788 insulator strings, 2636 good shells, 1140 broken shells, and 2004 flashover shells. The CPLID dataset consists of 848 images, including 600 images of normal insulators and 248 images of Defective\_Insulators. A notable feature of these defect images is that they are all artificially synthesized by manually pasting defective insulators onto different background images. Each dataset was split into training and validation sets at an 8:2 ratio.

\subsection{Experimental Setup}

All experiments in this study were conducted on the same high-performance server, with consistent hardware and software environments to eliminate interference from computational platform differences on the experimental results. The core hardware for the experiments included an Intel Core i9-13900KF processor and an NVIDIA GeForce RTX 4090 graphics processor, the latter providing powerful parallel computing support for model training. On the software side, we implemented the algorithms based on Python 3.10 and the PyTorch 2.2.2 framework, and used the CUDA 12.1 library for GPU acceleration. The specific configuration information is shown in Table~\ref{tab:hardware}.

\begin{table}[!t]
\caption{Hardware and Software Configuration for the Experiments\label{tab:hardware}}
\centering
\begin{tabular}{ll}
\toprule
\textbf{Item} & \textbf{Value} \\
\midrule
OS & Ubuntu 20.04 LTS \\
CPU & 13th Gen Intel(R) Core(TM) i9-13900KF \\
GPU & NVIDIA GeForce RTX 4090 \\
Python & 3.10 \\
CUDA & 12.1 \\
PyTorch & 2.2.2 \\
\bottomrule
\end{tabular}
\end{table}

In the implementation of model training, we unified key hyperparameter settings to ensure fair comparison. We used the Stochastic Gradient Descent (SGD) optimizer for end-to-end model training. The initial learning rate was set to 0.01, the momentum factor was 0.937, and a weight decay coefficient of 0.0005 was used to suppress overfitting. The entire training process consisted of 300 epochs. To improve data processing efficiency, we set the batch size to 16 and enabled 4 workers for parallel data loading. The detailed settings of all key hyperparameters are summarized in Table~\ref{tab:params}.

\begin{table}[!t]
\caption{Experimental Parameters\label{tab:params}}
\centering
\begin{tabular}{ll}
\toprule
\textbf{Item} & \textbf{Value} \\
\midrule
Optimizer & SGD \\
Learning Rate & 0.01 \\
Epochs & 300 \\
Momentum & 0.937 \\
Batch Size & 16 \\
Workers & 4 \\
Weight Decay Factor & 0.0005 \\
\bottomrule
\end{tabular}
\end{table}

\subsection{Evaluation Metrics}

The model's performance is evaluated based on two key aspects: computational efficiency and detection accuracy. Model efficiency and complexity are assessed using three metrics. \textbf{Parameters} (M) represent the total number of learnable weights and biases, indicating the model's scale and memory footprint. \textbf{Model Size} (MB) is the disk space required to store the trained weights, a direct measure of storage requirements. \textbf{GFLOPs} (Giga Floating-point Operations) quantifies the computational workload for a single forward pass; a lower value signifies higher efficiency, which is critical for real-time deployment on resource-constrained devices. Detection accuracy is assessed through a hierarchy of metrics built upon the concepts of True Positives (TP), False Positives (FP), and False Negatives (FN). From these, we define \textbf{Precision}, the ratio of correct detections to all positive predictions, and \textbf{Recall}, the ratio of correct detections to all ground-truth objects.

\begin{equation}
\text{Precision} = \frac{\text{TP}}{\text{TP} + \text{FP}}
\end{equation}

\begin{equation}
\text{Recall} = \frac{\text{TP}}{\text{TP} + \text{FN}}
\end{equation}

To balance the inherent trade-off between these two, the \textbf{F1-score} is calculated as their harmonic mean:
\begin{equation}
\text{F1} = 2 \times \frac{\text{Precision} \times \text{Recall}}{\text{Precision} + \text{Recall}}
\end{equation}
For a more comprehensive single-class evaluation, \textbf{Average Precision (AP)} is used. Calculated as the area under the Precision-Recall (P-R) curve, AP summarizes performance across all confidence thresholds.
\begin{equation}
\text{AP} = \int_{0}^{1} p(r) dr
\end{equation}
Finally, the \textbf{mean Average Precision (mAP)} serves as the primary overall metric, assessing performance across all $N$ classes by averaging their individual AP scores.
\begin{equation}
\text{mAP} = \frac{1}{N} \sum_{i=1}^{N} \text{AP}_i
\end{equation}

\subsection{Experimental Results Analysis}

To comprehensively evaluate the performance of the proposed Context-Enhanced and Aligned YOLO (YOLO-CEA) model for insulator defect detection, we conducted a series of experiments on the public Insulator Defect Image Dataset (IDID). We first performed independent component validation for the core modules—C3k2\_HTC, ABFPN, and SLQMHead—then examined the synergistic effects of each module through ablation studies, and finally compared the overall performance of our model with current mainstream object detection algorithms.

\subsubsection{Experimental Validation and Analysis of the C3k2 Module}

In the backbone network, our designed \textbf{C3k2-HTC module} integrates the global context-aware capabilities of Transformers with the local detail extraction capabilities of CNNs, aiming to enhance the model's ability to represent defect features in complex backgrounds. As shown in Table~\ref{tab:c3k2_results}, compared to the baseline C3k2, C3k2-HTC improved the AP50 scores for breakage and flashover defects by 1.42\% and 0.70\%, reaching 97.78\% and 98.15\%, respectively. This result confirms that by processing local and global information in parallel and enhancing the feed-forward network with a Gated Linear Unit (CGLU), C3k2-HTC can extract more discriminative features. Notably, this performance gain was accompanied by a reduction in parameters and computational load (from 2.58M/6.3 GFLOPs to 2.45M/6.2 GFLOPs), fully demonstrating the efficiency and effectiveness of its design.

\begin{table*}[!htbp]
\caption{Performance Comparison of C3k2 Module Variants\label{tab:c3k2_results}}
\centering
\footnotesize
\begin{tabular}{lcccccccc}
\toprule
\multirow{2}{*}{\textbf{Method}} & \multicolumn{2}{c}{\textbf{Breakage}} & \multicolumn{2}{c}{\textbf{Flashover}} & \textbf{Parameters} & \textbf{GFLOPs} & \textbf{Weight Size} \\
\cmidrule(lr){2-3} \cmidrule(lr){4-5}
 & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{(M)} & & \textbf{(MB)} \\
\midrule
C3k2 & 96.36 & 77.00 & 97.45 & 86.63 & 2.58 & 6.3 & 5.2 \\
C3k2-faster\cite{liu2024yolo} & 91.01 & 70.30 & 95.72 & 82.30 & 2.28 & 5.8 & 4.7 \\
C3k2-star\cite{jin2025real} & 95.08 & 77.06 & 97.44 & 86.77 & 2.47 & 6.4 & 5.1 \\
C3k2\_HTC & \textbf{97.78} & \textbf{80.41} & \textbf{98.15} & \textbf{86.84} & \textbf{2.45} & \textbf{6.2} & \textbf{5.0} \\
\bottomrule
\end{tabular}
\end{table*}

\begin{figure*}[!htbp]
\centering
\includegraphics[width=\textwidth]{figures/hotmap}
\caption{Visualization comparison of attention heatmaps generated by different C3k2 module variants. The figure shows how C3k2-HTC achieves more focused attention on insulator features while suppressing background noise compared to baseline methods.}
\label{fig_hotmap}
\end{figure*}

To further reveal its internal mechanism, we conducted a visualization analysis of the feature maps, as shown in Figure~\ref{fig_hotmap}. The figure compares the attention heatmaps of different C3k2 variants across several images with complex backgrounds. It is evident that baseline models like C3k2 and its alternatives (C3k2-Faster, C3k2-Star) exhibit diffuse attention, where high-activation areas (in red) are scattered and often spill into irrelevant background elements. For instance, in columns (a) and (c), these models are clearly distracted by the textured wall and surrounding foliage, respectively. In stark contrast, the heatmaps generated by our C3k2-HTC module demonstrate a significantly more focused and precise attention pattern. The activation is tightly concentrated on the salient structural features of the insulator, specifically its sheds, while effectively suppressing noise from the background. This visual evidence intuitively demonstrates that the C3k2-HTC module excels at enhancing target saliency and extracting more discriminative features, directly corroborating the quantitative performance gains reported in Table~\ref{tab:c3k2_results}.

\subsubsection{Experimental Validation and Analysis of FPN}

In the feature fusion neck, our designed \textbf{Aligned and Balanced Feature Pyramid Network (ABFPN)} demonstrated a significant advantage in multi-scale feature processing. According to the data in Table~\ref{tab:fpn_results}, ABFPN increased the AP50-95 for breakage defects from 77.00\% to 80.85\% with almost no increase in computational cost (6.3 GFLOPs), outperforming other FPN variants in overall performance. This is mainly attributed to its internal mechanisms: first, the SNI module in the top-down path effectively mitigates feature misalignment during upsampling through energy-aware soft interpolation; second, the GSE module in the bottom-up path reduces computational overhead while preserving rich feature information. Ultimately, ABFPN generates better-aligned and more information-balanced multi-scale feature maps, laying a solid foundation for subsequent accurate detection.

\begin{table*}[!htbp]
\caption{Performance Comparison of FPN Variants\label{tab:fpn_results}}
\centering
\footnotesize
\begin{tabular}{lcccccccc}
\toprule
\multirow{2}{*}{\textbf{Method}} & \multicolumn{2}{c}{\textbf{Breakage}} & \multicolumn{2}{c}{\textbf{Flashover}} & \textbf{Parameters} & \textbf{GFLOPs} & \textbf{Weight Size} \\
\cmidrule(lr){2-3} \cmidrule(lr){4-5}
 & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{(M)} & & \textbf{(MB)} \\
\midrule
FPN-PAN & 96.36 & 77.00 & 97.45 & 86.63 & 2.58 & 6.3 & 5.2 \\
AFPN\cite{10471592} & 96.63 & 78.96 & 98.42 & 88.46 & 2.65 & 8.8 & 5.5 \\
BiFPN\cite{tan2020efficientdet} & 96.13 & 78.53 & 98.09 & 86.30 & 1.92 & 6.3 & 4.0 \\
ABFPN & \textbf{97.85} & \textbf{80.85} & \textbf{97.87} & \textbf{87.06} & \textbf{2.53} & \textbf{6.3} & \textbf{5.2} \\
\bottomrule
\end{tabular}
\end{table*}

\subsubsection{Experimental Validation and Analysis of the Head}

At the detection head level, our proposed \textbf{Shared Location Quality Modulation Detection Head (SLQMHead)} aims to improve prediction reliability. As shown in Table~\ref{tab:head_results}, this detection head achieved the highest AP50 of 98.36\% for flashover defect detection while significantly reducing the model's computational complexity (5.6 GFLOPs) and parameter count. This validates the effectiveness of its core LQM module, which dynamically calibrates classification confidence by evaluating the quality of localization predictions. This mechanism fundamentally promotes synergy between classification and regression tasks, ensuring the high reliability of detection results in terms of both class and location.

\begin{table*}[!htbp]
\caption{Performance Comparison of Detection Head Variants\label{tab:head_results}}
\centering
\footnotesize
\begin{tabular}{lcccccccc}
\toprule
\multirow{2}{*}{\textbf{Method}} & \multicolumn{2}{c}{\textbf{Breakage}} & \multicolumn{2}{c}{\textbf{Flashover}} & \textbf{Parameters} & \textbf{GFLOPs} & \textbf{Weight Size} \\
\cmidrule(lr){2-3} \cmidrule(lr){4-5}
 & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{(M)} & & \textbf{(MB)} \\
\midrule
Head & 96.36 & 77.00 & 97.45 & 86.63 & 2.58 & 6.3 & 5.2 \\
LUFDH\cite{wu2025crl} & 97.80 & 77.64 & 97.59 & 87.18 & 2.42 & 5.6 & 4.9 \\
LSCSBD\cite{jin2025real} & 97.40 & 80.66 & 97.80 & 87.99 & 2.45 & 6.2 & 5.0 \\
SLQMHead & \textbf{96.96} & \textbf{77.96} & \textbf{98.36} & \textbf{87.36} & \textbf{2.42} & \textbf{5.6} & \textbf{4.9} \\
\bottomrule
\end{tabular}
\end{table*}

\subsubsection{Ablation Study}

To investigate the synergistic gains among the innovative modules, we designed a comprehensive ablation study (see Table~\ref{tab:ablation}). The results clearly show a progressive improvement in performance. The baseline model's AP50-95 was 83.03\%, and introducing any single module brought performance gains. Among them, ABFPN's contribution was the most significant, once again highlighting the central role of feature alignment in the insulator detection task.

\begin{table*}[!htbp]
\caption{Ablation Study Results\label{tab:ablation}}
\centering
\footnotesize
\begin{tabular}{ccccccccccc}
\toprule
\textbf{C3k2\_HTC} & \textbf{ABFPN} & \textbf{SLQMH} & \textbf{GFLOPs} & \textbf{Model Size} & \textbf{P/\%} & \textbf{R/\%} & \textbf{F1/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} \\
 &  &  &  & \textbf{(MB)} &  &  &  &  &  \\
\midrule
$\times$ & $\times$ & $\times$ & 6.3 & 5.2 & 97.39 & 98.68 & 98.03 & 97.13 & 83.03 \\
$\checkmark$ & $\times$ & $\times$ & 6.2 & 5.0 & 95.92 & 94.44 & 95.15 & 98.10 & 84.61 \\
$\times$ & $\checkmark$ & $\times$ & 6.3 & 5.2 & 95.77 & 94.08 & 94.88 & 97.98 & 85.07 \\
$\times$ & $\times$ & $\checkmark$ & 5.6 & 4.9 & 93.71 & 95.58 & 94.59 & 97.97 & 84.03 \\
$\checkmark$ & $\checkmark$ & $\times$ & 6.1 & 4.9 & 95.72 & 92.78 & 94.16 & 97.45 & 84.17 \\
$\checkmark$ & $\times$ & $\checkmark$ & 5.5 & 4.7 & 95.50 & 93.69 & 94.50 & 97.77 & 83.53 \\
$\times$ & $\checkmark$ & $\checkmark$ & 5.6 & 4.8 & 95.54 & 93.06 & 94.21 & 97.35 & 83.58 \\
$\checkmark$ & $\checkmark$ & $\checkmark$ & \textbf{5.4} & \textbf{4.6} & \textbf{96.19} & \textbf{96.07} & \textbf{96.11} & \textbf{98.36} & \textbf{87.81} \\
\bottomrule
\end{tabular}
\end{table*}

Crucially, when all three modules were integrated, the model's performance showed a significant synergistic effect rather than a simple sum. The final YOLO-CEA model achieved an AP50-95 of \textbf{87.81\%}, a remarkable improvement of \textbf{4.78 percentage points} over the baseline model. At the same time, the model's GFLOPs and size were reduced to \textbf{5.4} and \textbf{4.6MB}, respectively, making it the most efficient configuration of all. This fully demonstrates that the context-enhanced features from C3k2-HTC, the aligned and balanced features from ABFPN, and the high-reliability predictions from SLQMHead form a highly complementary system that achieves an overall performance optimization of the baseline model.

\begin{figure*}[!htbp]
\centering
\includegraphics[width=\textwidth]{figures/compared}
\caption{Qualitative comparison of detection results between baseline YOLOv11n and proposed YOLO-CEA across diverse challenging scenarios. The comparison demonstrates YOLO-CEA's superior confidence scores and reduced missed detections in complex backgrounds.}
\label{fig_compared}
\end{figure*}

To visually substantiate the quantitative improvements demonstrated in our ablation studies, we present a qualitative comparison of detection results between the baseline YOLOv11n model and our proposed YOLO-CEA, as shown in Figure~\ref{fig_compared}. Across various challenging scenarios featuring complex backgrounds, different insulator types, and varied viewing angles, the superiority of YOLO-CEA is evident. For instance, in the second row, while the baseline model identifies the broken insulator sheds, it does so with extremely low confidence scores (0.28, 0.29), making them prone to being discarded. In contrast, YOLO-CEA confidently and correctly classifies the same defects with a high score of 0.92. Furthermore, YOLO-CEA demonstrates a lower rate of missed detections, successfully identifying all insulator sheds in strings where the baseline model fails, such as in the first and fifth rows. This enhanced recall and improved confidence, even against cluttered backgrounds like foliage (fourth row) and in unconventional aerial views (fifth row), intuitively confirms that the synergy of the C3k2-HTC, ABFPN, and SLQMD modules equips our model with significantly greater accuracy and robustness for real-world insulator inspection tasks.

\subsubsection{Comparative Experiments}

Table~\ref{tab:comparison} compares the comprehensive performance of YOLO-CEA with a series of mainstream object detection algorithms. The experimental results show that compared to two-stage detectors like Faster R-CNN, YOLO-CEA comprehensively surpasses them in accuracy, while its model size and computational load are far lower. In comparison with lightweight models from the YOLO series, YOLO-CEA also performs exceptionally well. For example, compared to the baseline model YOLOv11n, YOLO-CEA improved the AP50-95 score for flashover defects by 4.05 percentage points while reducing the computational load by 14.3\%. It is particularly noteworthy that when compared with the latest MCI-GLA (2024 TIM) model, although the latter has an advantage in a single metric, YOLO-CEA leads in three key metrics: \textbf{Breakage AP50, Flashover AP50, and Flashover AP50-95}. Furthermore, its parameter count and computational load are only \textbf{5.5\% and 5.1\%} of MCI-GLA's, respectively.

\begin{table*}[!htbp]
\caption{Performance Comparison with State-of-the-Art Methods\label{tab:comparison}}
\centering
\footnotesize
\begin{tabular}{lcccccccc}
\toprule
\multirow{2}{*}{\textbf{Method}} & \multicolumn{2}{c}{\textbf{Breakage}} & \multicolumn{2}{c}{\textbf{Flashover}} & \textbf{Parameters} & \textbf{GFLOPs} & \textbf{Weight Size} \\
\cmidrule(lr){2-3} \cmidrule(lr){4-5}
 & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{(M)} & & \textbf{(MB)} \\
\midrule
Faster R-CNN\cite{ren2016faster} & 90.8 & 57.7 & 88.8 & 65.8 & 41.75 & 134 & 159.5 \\
Cascade R-CNN\cite{cai2018cascade} & 91.8 & 64.2 & 91.0 & 72.0 & 69.39 & 162 & 265.0 \\
Dynamic R-CNN\cite{zhang2020dynamic} & 95.0 & 68.2 & 92.5 & 71.3 & 41.75 & 134 & 159.5 \\
Libra R-CNN\cite{pang2019libra} & 91.2 & 60.5 & 87.9 & 67.7 & 42.02 & 136 & 160.5 \\
RetinaNet\cite{lin2017focal} & 84.3 & 47.6 & 67.0 & 40.7 & 34.31 & 154.17 & 130.9 \\
YOLOv3-tiny\cite{yolov3} & 97.37 & 79.89 & 97.35 & 87.13 & 12.12 & 18.9 & 23.3 \\
YOLOv5n\cite{yolov5} & 96.58 & 77.43 & 98.30 & 87.08 & 2.50 & 7.1 & 5.0 \\
YOLOv6n\cite{yolov6} & 95.30 & 78.32 & 97.71 & 87.04 & 4.23 & 11.8 & 8.3 \\
YOLOv7-tiny\cite{yolov7} & 92.87 & 68.07 & 93.65 & 78.94 & 6.01 & 11.7 & 13.0 \\
YOLOv8n\cite{yolov8} & 98.20 & 81.05 & 98.36 & 87.94 & 3.00 & 8.1 & 6.0 \\
YOLOv9t\cite{yolov9} & 96.23 & 80.38 & 97.93 & 87.96 & 1.97 & 7.6 & 4.4 \\
YOLOv10n\cite{yolov10} & 95.55 & 79.20 & 97.39 & 86.94 & 2.69 & 8.2 & 5.5 \\
YOLOv11n\cite{yolo11} & 96.36 & 77.00 & 97.45 & 86.63 & 2.58 & 6.3 & 5.2 \\
YOLO12n\cite{yolov12} & 95.48 & 78.43 & 98.18 & 88.01 & 2.55 & 6.3 & 5.3 \\
MCI-GLA\cite{wang2024mci} & 95.6 & \textbf{85.7} & 96.6 & 86.6 & 40.6 & 106.4 & 97.4 \\
\textbf{YOLO-CEA} & \textbf{97.76} & 82.80 & \textbf{98.58} & \textbf{90.68} & \textbf{2.25} & \textbf{5.4} & \textbf{4.6} \\
\bottomrule
\end{tabular}
\end{table*}

To further validate the generalization performance of the YOLO-CEA model, we evaluated it on another public dataset, CPLID, and compared it with several advanced models. The experimental results are shown in Table~\ref{tab:cplid_results}.

\begin{table*}[!htbp]
\caption{Performance Comparison on CPLID Dataset\label{tab:cplid_results}}
\centering
\footnotesize
\begin{tabular}{lcccccccc}
\toprule
\textbf{Method} & \textbf{Parameters} & \textbf{GFLOPs} & \textbf{P/\%} & \textbf{R/\%} & \textbf{F1/\%} & \textbf{mAP/\%} \\
 & \textbf{(M)} & & & & & \\
\midrule
YOLOv3-tiny\cite{yolov3} & 12.12 & 18.9 & 97.60 & 89.40 & 93.14 & 92.81 \\
YOLOv5n\cite{yolov5} & 2.50 & 7.1 & 96.48 & 98.81 & 97.63 & 99.18 \\
YOLOv6n\cite{yolov6} & 4.23 & 11.8 & 98.26 & 98.68 & 98.47 & 99.32 \\
YOLOv7t\cite{yolov7} & 6.01 & 13.0 & 96.93 & 99.01 & 97.95 & 98.69 \\
YOLOv8n\cite{yolov8} & 3.00 & 8.1 & 98.21 & 97.74 & 97.97 & 99.31 \\
YOLOv9t\cite{yolov9} & 1.97 & 7.6 & 96.97 & 98.87 & 97.91 & 99.27 \\
YOLOv10n\cite{yolov10} & 2.69 & 8.2 & 94.99 & 96.40 & 95.65 & 98.58 \\
YOLOv11n\cite{yolo11} & 2.58 & 6.3 & 97.39 & 98.68 & 98.03 & 99.08 \\
YOLO12n\cite{yolov12} & 2.55 & 6.3 & 96.76 & 99.25 & 97.98 & 99.19 \\
SnakeNet\cite{tao2024snakenet} & 2.90 & 6.70 & 99.70 & 99.80 & 94.00 & 99.50 \\
method in \cite{pradeep2025improved} & 2.93 & 6.90 & 99.84 & 99.92 & 98.88 & 99.66 \\
\textbf{YOLO-CEA} & \textbf{2.25} & \textbf{5.4} & \textbf{96.75} & \textbf{98.94} & \textbf{97.83} & \textbf{99.32} \\
\bottomrule
\end{tabular}
\end{table*}

YOLO-CEA also demonstrated excellent performance on this dataset, achieving an mAP of 99.32\% and a balanced performance in precision (P), recall (R), and F1-score. In terms of model efficiency, YOLO-CEA's parameter count (2.25M) and computational load (5.4 GFLOPs) remained the lowest among all compared models, once again proving the success of its lightweight design.

\section{Conclusion}
\label{sec:conclusion}

To address the challenge of balancing accuracy and efficiency in insulator defect detection within complex power line scenarios, this paper proposes a novel detection framework named \textbf{Context-Enhanced and Aligned YOLO (YOLO-CEA)}. This framework systematically reconstructs the backbone, feature fusion neck, and detection head of the baseline model YOLOv11, aiming to enhance detection performance from three aspects: \textbf{1) enhancing global context awareness through the Transformer-fused C3k2-HTC module; 2) achieving precise and balanced multi-scale feature alignment through the AB-FPN, which employs soft interpolation (SNI) and efficient convolution (GSE); and 3) improving parameter efficiency and enhancing prediction reliability through the SLQMD with its location quality modulation mechanism.}

Comprehensive experiments on the public IDID dataset fully validate the outstanding performance and sound design of YOLO-CEA. \textbf{Component validation experiments} show that the three innovative modules—C3k2-HTC, ABFPN, and SLQMD—can each improve detection accuracy for specific categories while effectively reducing or maintaining the model's computational complexity. The \textbf{ablation study} further reveals a significant synergistic effect among the modules: when all three are integrated, the final YOLO-CEA model achieves an AP50-95 of \textbf{87.81\%}, a substantial improvement of \textbf{4.78 percentage points} over the baseline. Critically, this performance leap is accompanied by a reduction in model complexity, with the final model's computational load (GFLOPS) and size (MB) decreasing to \textbf{5.4 GFLOPs} and \textbf{4.6 MB}, respectively, making it the most efficient configuration of all and proving the effectiveness of our design.

In the \textbf{comparative experiments} against mainstream algorithms, YOLO-CEA's advantages are even more pronounced. It not only comprehensively surpasses various classic YOLO variants in both accuracy and efficiency but also outperforms the latest MCI-GLA (2024 TIM) model, achieving leading scores in several key metrics for breakage and flashover defects with only \textbf{5.5\% of its parameters and 5.1\% of its computational load}. Furthermore, in tests on the CPLID dataset, YOLO-CEA also achieved a high mAP of \textbf{99.32\%} while maintaining the lowest computational overhead among all compared models, strongly demonstrating its powerful \textbf{generalization ability} and robustness across different scenarios.

In conclusion, the YOLO-CEA model proposed in this study, through a synergistic design of context enhancement, feature alignment, and reliability modulation, successfully achieves an excellent balance between SOTA-level detection accuracy and extreme computational efficiency for the task of insulator defect detection. This provides a viable solution for developing lightweight, high-performance intelligent inspection systems, holding significant academic value and broad engineering application prospects. Despite these significant achievements, future work can be further deepened by expanding the dataset with more diverse and extreme operating conditions, as well as by conducting deployment validation on actual drone hardware.

\section*{Acknowledgments}
[Acknowledgments section to be completed by the authors]

\bibliographystyle{IEEEtran}
\bibliography{reference}

\end{document}

