This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.9.4)  8 AUG 2025 11:29
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**yolo_cea_paper
(./yolo_cea_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(./IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count184
\@IEEEtrantmpcountB=\count185
\@IEEEtrantmpcountC=\count186
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1086.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1086.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1086.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@IEEEsubequation=\count191
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count192
\c@table=\count193
\@IEEEeqnnumcols=\count194
\@IEEEeqncolcnt=\count195
\@IEEEsubeqnnumrollback=\count196
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count197
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count198
\@IEEEtranrubishbin=\box52
) (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (d:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count199
\l__pdf_internal_box=\box53
\g__pdf_backend_object_int=\count266
\g__pdf_backend_annotation_int=\count267
\g__pdf_backend_link_int=\count268
))
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count269
\l__fontspec_language_int=\count270
\l__fontspec_strnum_int=\count271
\l__fontspec_tmp_int=\count272
\l__fontspec_tmpa_int=\count273
\l__fontspec_tmpb_int=\count274
\l__fontspec_tmpc_int=\count275
\l__fontspec_em_int=\count276
\l__fontspec_emdef_int=\count277
\l__fontspec_strong_int=\count278
\l__fontspec_strongdef_int=\count279
\l__fontspec_tmpa_dim=\dimen164
\l__fontspec_tmpb_dim=\dimen165
\l__fontspec_tmpc_dim=\dimen166
 (d:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (d:/texlive/2024/texmf-dist/tex/latex/unicode-math/unicode-math.sty
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/unicode-math/unicode-math-xetex.sty
Package: unicode-math-xetex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-02-18 LaTeX2e option processing using LaTeX3 keys
) (d:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen167
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen168
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count280
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count281
\leftroot@=\count282
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count283
\DOTSCASE@=\count284
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen169
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count285
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count286
\dotsspace@=\muskip16
\c@parentequation=\count287
\dspbrk@lvl=\count288
\tag@help=\toks19
\row@=\count289
\column@=\count290
\maxfields@=\count291
\andhelp@=\toks20
\eqnshift@=\dimen170
\alignsep@=\dimen171
\tagshift@=\dimen172
\tagwidth@=\dimen173
\totwidth@=\dimen174
\lineht@=\dimen175
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
\g__um_fam_int=\count292
\g__um_fonts_used_int=\count293
\l__um_primecount_int=\count294
\g__um_primekern_muskip=\muskip17
 (d:/texlive/2024/texmf-dist/tex/latex/unicode-math/unicode-math-table.tex)))

Package fontspec Info: Font family 'TeXGyreTermes(0)' created for font 'TeX
(fontspec)             Gyre Termes' with options [Ligatures=TeX].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/B/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: <->"TeX
(fontspec)             Gyre
(fontspec)             Termes/B/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/I/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Termes/I/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/BI/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Termes/BI/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"


Package fontspec Info: Font family 'TeXGyreTermes(1)' created for font 'TeX
(fontspec)             Gyre Termes' with options [].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/OT:script=latn;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/B/OT:script=latn;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: <->"TeX
(fontspec)             Gyre Termes/B/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/I/OT:script=latn;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Termes/I/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Termes/BI/OT:script=latn;language=dflt;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Termes/BI/OT:script=latn;language=dflt;+smcp;"

LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/TeXGyreTermes(1)/m/n on input line 11.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/TeXGyreTermes(1)/m/it on input line 11.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/TeXGyreTermes(1)/b/n on input line 11.

Package fontspec Info: Could not resolve font "TeX Gyre Termes Math/B" (it
(fontspec)             probably doesn't exist).


Package fontspec Info: Font family 'TeXGyreTermesMath(0)' created for font
(fontspec)             'TeX Gyre Termes Math' with options
(fontspec)             [BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"TeX Gyre Termes
(fontspec)             Math/OT:script=math;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 


Package fontspec Info: Could not resolve font "TeX Gyre Termes Math/B" (it
(fontspec)             probably doesn't exist).


Package fontspec Info: Font family 'TeXGyreTermesMath(1)' created for font
(fontspec)             'TeX Gyre Termes Math' with options
(fontspec)             [BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.7-},{Size=6.45-8.7,Font=TeX
(fontspec)             Gyre Termes Math,Style=MathScript},{Size=-6.45,Font=TeX
(fontspec)             Gyre Termes Math,Style=MathScriptScript}}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <8.7->"TeX Gyre
(fontspec)             Termes
(fontspec)             Math/OT:script=math;language=dflt;"<6.45-8.7>"TeX Gyre
(fontspec)             Termes
(fontspec)             Math/OT:script=math;language=dflt;+ssty=0;"<-6.45>"TeX
(fontspec)             Gyre Termes Math/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 

LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 13.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/TeXGyreTermesMath(1)/m/n on input line 13.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 13.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/TeXGyreTermesMath(1)/b/n on input line 13.

Package fontspec Info: TeX Gyre Termes Math scale = 1.0001.


Package fontspec Info: Could not resolve font "TeX Gyre Termes Math/B" (it
(fontspec)             probably doesn't exist).


Package fontspec Info: TeX Gyre Termes Math scale = 1.0001.


Package fontspec Info: TeX Gyre Termes Math scale = 1.0001.


Package fontspec Info: TeX Gyre Termes Math scale = 1.0001.


Package fontspec Info: Font family 'TeXGyreTermesMath(2)' created for font
(fontspec)             'TeX Gyre Termes Math' with options
(fontspec)             [BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.7-},{Size=6.45-8.7,Font=TeX
(fontspec)             Gyre Termes Math,Style=MathScript},{Size=-6.45,Font=TeX
(fontspec)             Gyre Termes
(fontspec)             Math,Style=MathScriptScript}},ScaleAgain=1.0001,FontAdjustment={\fontdimen
(fontspec)             8\font =6.34pt\relax \fontdimen 9\font =4.3pt\relax
(fontspec)             \fontdimen 10\font =4.3pt\relax \fontdimen 11\font
(fontspec)             =5.98pt\relax \fontdimen 12\font =2.66pt\relax
(fontspec)             \fontdimen 13\font =3.39pt\relax \fontdimen 14\font
(fontspec)             =3.39pt\relax \fontdimen 15\font =2.84pt\relax
(fontspec)             \fontdimen 16\font =2.22pt\relax \fontdimen 17\font
(fontspec)             =2.22pt\relax \fontdimen 18\font =2.22pt\relax
(fontspec)             \fontdimen 19\font =1.11pt\relax \fontdimen 22\font
(fontspec)             =2.5pt\relax \fontdimen 20\font =0pt\relax \fontdimen
(fontspec)             21\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <8.7->s*[1.0001]"TeX
(fontspec)             Gyre Termes
(fontspec)             Math/OT:script=math;language=dflt;"<6.45-8.7>s*[1.0001]"TeX
(fontspec)             Gyre Termes
(fontspec)             Math/OT:script=math;language=dflt;+ssty=0;"<-6.45>s*[1.0001]"TeX
(fontspec)             Gyre Termes Math/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 8\font =6.34pt\relax \fontdimen 9\font
(fontspec)             =4.3pt\relax \fontdimen 10\font =4.3pt\relax \fontdimen
(fontspec)             11\font =5.98pt\relax \fontdimen 12\font =2.66pt\relax
(fontspec)             \fontdimen 13\font =3.39pt\relax \fontdimen 14\font
(fontspec)             =3.39pt\relax \fontdimen 15\font =2.84pt\relax
(fontspec)             \fontdimen 16\font =2.22pt\relax \fontdimen 17\font
(fontspec)             =2.22pt\relax \fontdimen 18\font =2.22pt\relax
(fontspec)             \fontdimen 19\font =1.11pt\relax \fontdimen 22\font
(fontspec)             =2.5pt\relax \fontdimen 20\font =0pt\relax \fontdimen
(fontspec)             21\font =0pt\relax 

LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `normal' on input line 13.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> TU/TeXGyreTermesMath(2)/m/n on input line 13.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `bold' on input line 13.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> TU/TeXGyreTermesMath(2)/b/n on input line 13.

Package fontspec Info: TeX Gyre Termes Math scale = 0.9999.


Package fontspec Info: Could not resolve font "TeX Gyre Termes Math/B" (it
(fontspec)             probably doesn't exist).


Package fontspec Info: TeX Gyre Termes Math scale = 0.9999.


Package fontspec Info: TeX Gyre Termes Math scale = 0.9999.


Package fontspec Info: TeX Gyre Termes Math scale = 0.9999.


Package fontspec Info: Font family 'TeXGyreTermesMath(3)' created for font
(fontspec)             'TeX Gyre Termes Math' with options
(fontspec)             [BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.7-},{Size=6.45-8.7,Font=TeX
(fontspec)             Gyre Termes Math,Style=MathScript},{Size=-6.45,Font=TeX
(fontspec)             Gyre Termes
(fontspec)             Math,Style=MathScriptScript}},ScaleAgain=0.9999,FontAdjustment={\fontdimen
(fontspec)             8\font =0.52pt\relax \fontdimen 9\font =1.0pt\relax
(fontspec)             \fontdimen 10\font =1.0pt\relax \fontdimen 11\font
(fontspec)             =1.0pt\relax \fontdimen 12\font =4.33pt\relax
(fontspec)             \fontdimen 13\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <8.7->s*[0.9999]"TeX
(fontspec)             Gyre Termes
(fontspec)             Math/OT:script=math;language=dflt;"<6.45-8.7>s*[0.9999]"TeX
(fontspec)             Gyre Termes
(fontspec)             Math/OT:script=math;language=dflt;+ssty=0;"<-6.45>s*[0.9999]"TeX
(fontspec)             Gyre Termes Math/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 8\font =0.52pt\relax \fontdimen 9\font
(fontspec)             =1.0pt\relax \fontdimen 10\font =1.0pt\relax \fontdimen
(fontspec)             11\font =1.0pt\relax \fontdimen 12\font =4.33pt\relax
(fontspec)             \fontdimen 13\font =0pt\relax 

LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 13.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> TU/TeXGyreTermesMath(3)/m/n on input line 13.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 13.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> TU/TeXGyreTermesMath(3)/b/n on input line 13.

Package fontspec Info: Font family 'TeXGyreHeros(0)' created for font 'TeX
(fontspec)             Gyre Heros' with options [Ligatures=TeX].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/B/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: <->"TeX
(fontspec)             Gyre
(fontspec)             Heros/B/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/I/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Heros/I/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/BI/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Heros/BI/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"


Package fontspec Info: Font family 'TeXGyreHeros(1)' created for font 'TeX
(fontspec)             Gyre Heros' with options [].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/OT:script=latn;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/B/OT:script=latn;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: <->"TeX
(fontspec)             Gyre Heros/B/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/I/OT:script=latn;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Heros/I/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Heros/BI/OT:script=latn;language=dflt;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Heros/BI/OT:script=latn;language=dflt;+smcp;"

LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/TeXGyreHeros(1)/m/n on input line 14.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/TeXGyreHeros(1)/b/n on input line 14.

Package fontspec Info: Font family 'TeXGyreCursor(0)' created for font 'TeX
(fontspec)             Gyre Cursor' with options
(fontspec)             [WordSpace={1,0,0},HyphenChar=None,PunctuationSpace=WordSpace].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/OT:script=latn;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/OT:script=latn;language=dflt;+smcp;"
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 2\font =1\fontdimen 2\font \fontdimen 3\font
(fontspec)             =0\fontdimen 3\font \fontdimen 4\font =0\fontdimen
(fontspec)             4\font \fontdimen 7\font =0\fontdimen 2\font
(fontspec)             \tex_hyphenchar:D \font =-1\scan_stop: 
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/B/OT:script=latn;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: <->"TeX
(fontspec)             Gyre Cursor/B/OT:script=latn;language=dflt;+smcp;"
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 2\font =1\fontdimen 2\font \fontdimen 3\font
(fontspec)             =0\fontdimen 3\font \fontdimen 4\font =0\fontdimen
(fontspec)             4\font \fontdimen 7\font =0\fontdimen 2\font
(fontspec)             \tex_hyphenchar:D \font =-1\scan_stop: 
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/I/OT:script=latn;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Cursor/I/OT:script=latn;language=dflt;+smcp;"
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 2\font =1\fontdimen 2\font \fontdimen 3\font
(fontspec)             =0\fontdimen 3\font \fontdimen 4\font =0\fontdimen
(fontspec)             4\font \fontdimen 7\font =0\fontdimen 2\font
(fontspec)             \tex_hyphenchar:D \font =-1\scan_stop: 
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/BI/OT:script=latn;language=dflt;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Cursor/BI/OT:script=latn;language=dflt;+smcp;"
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 2\font =1\fontdimen 2\font \fontdimen 3\font
(fontspec)             =0\fontdimen 3\font \fontdimen 4\font =0\fontdimen
(fontspec)             4\font \fontdimen 7\font =0\fontdimen 2\font
(fontspec)             \tex_hyphenchar:D \font =-1\scan_stop: 


Package fontspec Info: Font family 'TeXGyreCursor(1)' created for font 'TeX
(fontspec)             Gyre Cursor' with options [].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/OT:script=latn;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/B/OT:script=latn;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: <->"TeX
(fontspec)             Gyre Cursor/B/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/I/OT:script=latn;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Cursor/I/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"TeX Gyre
(fontspec)             Cursor/BI/OT:script=latn;language=dflt;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"TeX Gyre
(fontspec)             Cursor/BI/OT:script=latn;language=dflt;+smcp;"

LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/TeXGyreCursor(1)/m/n on input line 16.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/TeXGyreCursor(1)/b/n on input line 16.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math delimiter \ulcorner on input line 74.
LaTeX Font Info:    Redeclaring math delimiter \urcorner on input line 75.
LaTeX Font Info:    Redeclaring math delimiter \llcorner on input line 76.
LaTeX Font Info:    Redeclaring math delimiter \lrcorner on input line 77.
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (d:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
\c@ALC@unique=\count295
\c@ALC@line=\count296
\c@ALC@rem=\count297
\c@ALC@depth=\count298
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (d:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count299
\float@exts=\toks24
\float@box=\box56
\@float@everytoks=\toks25
\@floatcapt=\box57
)
\@float@every@algorithm=\toks26
\c@algorithm=\count300
) (d:/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen176
\ar@mcellbox=\box58
\extrarowheight=\dimen177
\NC@list=\toks27
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box59
) (d:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen178
\lightrulewidth=\dimen179
\cmidrulewidth=\dimen180
\belowrulesep=\dimen181
\belowbottomsep=\dimen182
\aboverulesep=\dimen183
\abovetopsep=\dimen184
\cmidrulesep=\dimen185
\cmidrulekern=\dimen186
\defaultaddspace=\dimen187
\@cmidla=\count301
\@cmidlb=\count302
\@aboverulesep=\dimen188
\@belowrulesep=\dimen189
\@thisruleclass=\count303
\@lastruleclass=\count304
\@thisrulewidth=\dimen190
) (d:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip58
\multirow@cntb=\count305
\multirow@dima=\skip59
\bigstrutjot=\dimen191
) (d:/texlive/2024/texmf-dist/tex/latex/subfig/subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package
 (d:/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen192
\captionmargin=\dimen193
\caption@leftmargin=\dimen194
\caption@rightmargin=\dimen195
\caption@width=\dimen196
\caption@indent=\dimen197
\caption@parindent=\dimen198
\caption@hangindent=\dimen199
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEtablestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\normalfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptionsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\normalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONconference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi \fi  on input line 1175.
)
\c@KVtest=\count306
\sf@farskip=\skip60
\sf@captopadj=\dimen256
\sf@capskip=\skip61
\sf@nearskip=\skip62
\c@subfigure=\count307
\c@subfigure@save=\count308
\c@lofdepth=\count309
\c@subtable=\count310
\c@subtable@save=\count311
\c@lotdepth=\count312
\sf@top=\skip63
\sf@bottom=\skip64
) (d:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
) (d:/texlive/2024/texmf-dist/tex/latex/sttools/stfloats.sty
Package: stfloats 2017/03/27 v3.3 Improve float mechanism and baselineskip settings
\@dblbotnum=\count313
\c@dblbotnumber=\count314
) (d:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (d:/texlive/2024/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks28
\verbatim@line=\toks29
\verbatim@in@stream=\read2
) (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen257
\Gin@req@width=\dimen258
) (d:/texlive/2024/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (d:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (d:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (d:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (d:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count315
) (d:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count316
)
\@linkdim=\dimen259
\Hy@linkcounter=\count317
\Hy@pagecounter=\count318
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count319
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count320
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen260
 (d:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count321
\Field@Width=\dimen261
\Fld@charsize=\dimen262
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring ON on input line 6081.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
 (d:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count322
\c@Item=\count323
\c@Hfootnote=\count324
)
Package hyperref Info: Driver (autodetected): hxetex.
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-01-20 v7.01h Hyperref driver for XeTeX
 (d:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box60
\c@Hy@AnnotLevel=\count325
\HyField@AnnotCount=\count326
\Fld@listcount=\count327
\c@bookmark@seq@number=\count328
 (d:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (d:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip65
)

LaTeX Warning: Unused global option(s):
    [lettersize,nofonts].

No file yolo_cea_paper.aux.
\openout1 = `yolo_cea_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
-- Lines per column: 58 (exact).
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: End \AtBeginDocument code.
(d:/texlive/2024/texmf-dist/tex/latex/graphics/color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: xetex.def on input line 149.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx))
Package hyperref Info: Link coloring ON on input line 45.
\@outlinefile=\write3
\openout3 = `yolo_cea_paper.out'.



Package hyperref Warning: Rerun to get /PageLabels entry.


LaTeX Warning: No \author given.


LaTeX Warning: No \author given.


LaTeX Warning: No \author given.

./yolo_cea_paper.tex:77: Font \@IEEEPARstartsubfont=""TeX at "" not loadable: Metric (TFM) file or installed font not found.
<to be read again> 
                   \@IEEEtrantmpdimenA 
l.77 \IEEEPARstart{T}{he}
                          stable and reliable operation of the power grid sy...
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.

./yolo_cea_paper.tex:77: Missing number, treated as zero.
<to be read again> 
                   \relax 
l.77 \IEEEPARstart{T}{he}
                          stable and reliable operation of the power grid sy...
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

./yolo_cea_paper.tex:77: Illegal unit of measure (pt inserted).
<to be read again> 
                   \relax 
l.77 \IEEEPARstart{T}{he}
                          stable and reliable operation of the power grid sy...
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no T ("54) in font nullfont!
** WARNING: IEEEPARstart drop letter has zero height! (line 77)
 Forcing the drop letter font size to 10pt.
./yolo_cea_paper.tex:77: Font \@IEEEPARstartfont=""TeX at "" not loadable: Metric (TFM) file or installed font not found.
<to be read again> 
                   \@IEEEtrantmpdimenB 
l.77 \IEEEPARstart{T}{he}
                          stable and reliable operation of the power grid sy...
I wasn't able to read the size data for this font,
so I will ignore the font specification.
[Wizards can fix TFM files using TFtoPL/PLtoTF.]
You might try inserting a different font spec;
e.g., type `I\font<same font id>=<substitute font name>'.

./yolo_cea_paper.tex:77: Missing number, treated as zero.
<to be read again> 
                   \protect 
l.77 \IEEEPARstart{T}{he}
                          stable and reliable operation of the power grid sy...
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

./yolo_cea_paper.tex:77: Illegal unit of measure (pt inserted).
<to be read again> 
                   \protect 
l.77 \IEEEPARstart{T}{he}
                          stable and reliable operation of the power grid sy...
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no T ("54) in font nullfont!
Missing character: There is no T ("54) in font nullfont!

LaTeX Warning: Citation `2022Inspection' on page 1 undefined on input line 77.


LaTeX Warning: Citation `2024Region' on page 1 undefined on input line 78.


LaTeX Warning: Citation `2023Summary' on page 1 undefined on input line 79.


LaTeX Warning: Citation `2022An' on page 1 undefined on input line 79.


LaTeX Warning: Citation `2021Real' on page 1 undefined on input line 80.


LaTeX Warning: Citation `9400959' on page 1 undefined on input line 82.


LaTeX Warning: Citation `2021InsuDet' on page 1 undefined on input line 82.


LaTeX Warning: Citation `2022Improved' on page 1 undefined on input line 82.


LaTeX Warning: Citation `2021Improved' on page 1 undefined on input line 83.


LaTeX Warning: Citation `coatings13050880' on page 1 undefined on input line 83.


LaTeX Warning: Citation `han2022insulator' on page 1 undefined on input line 85.


LaTeX Warning: Citation `DBLP:journals/eaai/FengYYYLSZ25' on page 1 undefined on input line 85.


LaTeX Warning: Citation `2024CACS-YOLO' on page 1 undefined on input line 88.

[1


]

LaTeX Warning: Reference `sec:method' on page 2 undefined on input line 106.


LaTeX Warning: Reference `sec:experimental' on page 2 undefined on input line 106.


LaTeX Warning: Reference `sec:conclusion' on page 2 undefined on input line 106.


LaTeX Warning: Reference `sec:conclusion' on page 2 undefined on input line 106.


LaTeX Warning: Citation `yolo11' on page 2 undefined on input line 111.


LaTeX Warning: Reference `fig_architecture' on page 2 undefined on input line 111.

File: figures/overfig.pdf Graphic file (type pdf)
<use figures/overfig.pdf>

LaTeX Warning: Reference `fig_architecture' on page 2 undefined on input line 120.


LaTeX Warning: Citation `2021An' on page 2 undefined on input line 129.

[2]
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(2)/m/n' will be
(Font)              scaled to size 10.00107pt on input line 131.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(2)/m/n' will be
(Font)              scaled to size 7.40077pt on input line 131.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(2)/m/n' will be
(Font)              scaled to size 5.50058pt on input line 131.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(3)/m/n' will be
(Font)              scaled to size 9.99893pt on input line 131.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(3)/m/n' will be
(Font)              scaled to size 7.3992pt on input line 131.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(3)/m/n' will be
(Font)              scaled to size 5.4994pt on input line 131.
LaTeX Font Info:    Trying to load font information for U+msa on input line 131.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 131.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Underfull \vbox (badness 10000) has occurred while \output is active []

 [3]

LaTeX Warning: Citation `shi2024transnext' on page 4 undefined on input line 147.

File: figures/HTC.pdf Graphic file (type pdf)
<use figures/HTC.pdf>

LaTeX Warning: Citation `li2024rethinking' on page 4 undefined on input line 164.


LaTeX Warning: Reference `eq:sni' on page 4 undefined on input line 166.

File: figures/SNI.pdf Graphic file (type pdf)
<use figures/SNI.pdf>
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(2)/m/n' will be
(Font)              scaled to size 8.00085pt on input line 179.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(2)/m/n' will be
(Font)              scaled to size 6.00064pt on input line 179.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(2)/m/n' will be
(Font)              scaled to size 5.00053pt on input line 179.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(3)/m/n' will be
(Font)              scaled to size 7.99915pt on input line 179.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(3)/m/n' will be
(Font)              scaled to size 5.99936pt on input line 179.
LaTeX Font Info:    Font shape `TU/TeXGyreTermesMath(3)/m/n' will be
(Font)              scaled to size 4.99947pt on input line 179.

LaTeX Warning: Citation `chollet2017xception' on page 4 undefined on input line 185.


LaTeX Warning: Citation `hendrycks2016gaussian' on page 4 undefined on input line 185.


LaTeX Warning: Citation `zhang2018shufflenet' on page 4 undefined on input line 185.

[4